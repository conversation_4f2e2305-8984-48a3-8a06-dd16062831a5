# Avehub - Website Setup Guide 🚀 

Welcome to the Avehub website! Follow this simple guide to get everything set up and running locally.

## Prerequisites 📦

Before you begin, make sure you have the following installed:

- [Node.js](https://nodejs.org/) (v16 or above)
- [pnpm](https://pnpm.io/)
- [Doppler CLI](https://www.doppler.com/)

## Setup Steps 🛠️

Follow these steps to get started:

---

### 1. Install Project Dependencies 📥

Start by installing all the project dependencies using `pnpm`:

```bash
pnpm install
```


