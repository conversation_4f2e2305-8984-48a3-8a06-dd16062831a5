# Developer Console System - Setup Guide

## Overview

This document provides comprehensive setup instructions for the Developer Console system implemented in AvehubV1. The system includes:

- **Developer Console Dashboard** (`/developer-console`)
- **Public App Store** (`/apps` and `/apps/[id]`)
- **Admin App Approval System** (integrated into existing admin panel)
- **Cloudinary File Upload System**
- **Complete CRUD API for apps management**

## Required Environment Variables

Add the following environment variables to your `.env.local` file:

```env
# Custom CDN Configuration (Required for file uploads)
CDN_API_KEY=cccd3cd6a25cb4b40f1626ebfb0eae4973b8f52457e726b692332b7078ffa078

# Existing variables (ensure these are set)
DATABASE_URL=your_mongodb_connection_string
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

## Custom CDN Server Setup Instructions

1. **CDN Server Details**:
   - **Base URL**: `https://cdn.avehubs.com`
   - **API Endpoint**: `/api/upload`
   - **Authentication**: X-API-Key header
   - **Max File Size**: 500MB for apps, 10MB for images

2. **API Key Configuration**:
   - The provided API key has full upload permissions
   - No additional setup required - the key is pre-configured
   - Files are automatically distributed via CDN for fast global access

3. **Supported File Types**:
   - **Applications**: `.jar`, `.exe`, `.dmg`, `.deb`, `.rpm`, `.zip`, `.tar.gz`, `.msi`, `.apk`, `.ipa`
   - **Images**: `.jpg`, `.jpeg`, `.png`, `.webp`, `.gif`, `.svg`

## Database Migration

Run the following command to update your database schema:

```bash
npx prisma db push
```

This will add the new models and fields:
- `isDeveloper` field to User model
- `App` model with all required fields
- `AppVersion` model for version history
- `AppComment` model for user comments

## Features Implemented

### 1. Developer Console (`/developer-console`)
- **Protected Route**: Only accessible to users with `isDeveloper: true`
- **Dashboard**: Overview of developer's apps, downloads, and comments
- **App Management**: Create, edit, and delete applications
- **Analytics**: View download statistics and user engagement
- **File Upload**: Real-time progress indicators for app files and images

### 2. Public App Store (`/apps`)
- **Public Access**: No login required to browse apps
- **Search & Filter**: By category, name, description, and tags
- **Sorting**: By date, downloads, name
- **Grid/List View**: Toggle between different view modes
- **Responsive Design**: Optimized for all screen sizes

### 3. App Detail Pages (`/apps/[id]`)
- **Comprehensive Info**: Screenshots, description, developer info
- **Download Tracking**: Automatic download count increment
- **Comments System**: User reviews and ratings (login required)
- **Version History**: Track app updates and changes
- **Developer Contact**: Website and support email links

### 4. Admin Integration
- **App Approval**: Review and approve/reject submitted apps
- **Status Management**: Change app status (pending/approved/rejected/suspended)
- **Developer Management**: Grant/revoke developer permissions
- **Content Moderation**: Monitor and manage user comments

### 5. API Routes
- `GET/POST /api/apps` - List and create apps
- `GET/PATCH/DELETE /api/apps/[id]` - Individual app operations
- `POST /api/apps/[id]/download` - Track downloads
- `GET/POST /api/apps/[id]/comments` - Manage comments
- `POST /api/upload` - File upload to Custom CDN
- `POST /api/developer-check` - Verify developer status

## File Upload System

### Supported File Types
- **Applications**: `.jar`, `.exe`, `.dmg`, `.deb`, `.rpm`, `.zip`, `.tar.gz`, `.msi`, `.apk`, `.ipa`
- **Images**: `.jpg`, `.jpeg`, `.png`, `.webp`, `.gif`, `.svg`

### File Size Limits
- **Applications**: 500MB maximum
- **Images**: 10MB maximum

### Upload Features
- **Real-time Progress**: Visual progress bars during upload
- **Error Handling**: Comprehensive validation and error messages
- **Global CDN**: Files distributed globally via CDN for fast access
- **Secure Storage**: Files stored securely on custom CDN with authentication

## Security Features

### Route Protection
- **Middleware Integration**: Developer routes protected at middleware level
- **Permission Checking**: Real-time verification of developer status
- **Session Management**: Secure session handling with NextAuth

### File Upload Security
- **File Type Validation**: Strict file type checking
- **Size Limits**: Enforced file size restrictions
- **API Authentication**: Custom CDN requires API key for all uploads
- **Access Control**: Only authenticated developers can upload files

### Data Protection
- **Input Validation**: All user inputs validated and sanitized
- **SQL Injection Prevention**: Prisma ORM provides built-in protection
- **XSS Protection**: React's built-in XSS protection
- **CSRF Protection**: NextAuth provides CSRF protection

## Performance Optimizations

### Database
- **Indexed Fields**: Strategic indexing on frequently queried fields
- **Efficient Queries**: Optimized database queries with proper relations
- **Pagination**: Built-in support for paginated results

### Frontend
- **Code Splitting**: Automatic code splitting with Next.js
- **Image Optimization**: Next.js Image component with Custom CDN
- **Lazy Loading**: Components and images loaded on demand
- **Caching**: Proper caching strategies for API responses

### File Handling
- **CDN Delivery**: Custom CDN's global network for fast file delivery
- **Direct Access**: Files accessible via direct CDN URLs
- **Progressive Loading**: Progressive image loading for better UX

## Usage Instructions

### For Developers
1. **Get Developer Access**: Contact admin to set `isDeveloper: true`
2. **Access Console**: Navigate to `/developer-console`
3. **Create App**: Click "Create App" and fill in details
4. **Upload Files**: Use the file upload system for app files and images
5. **Submit for Review**: App will be submitted with "PENDING" status
6. **Track Performance**: Monitor downloads and user feedback

### For Users
1. **Browse Apps**: Visit `/apps` to see all available applications
2. **Search & Filter**: Use search and category filters to find apps
3. **View Details**: Click on any app to see detailed information
4. **Download**: Click download button to get the app file
5. **Leave Reviews**: Login to leave comments and ratings

### For Admins
1. **Review Apps**: Check pending apps in admin panel
2. **Approve/Reject**: Change app status as needed
3. **Manage Developers**: Grant developer permissions to users
4. **Monitor Activity**: Track app downloads and user engagement

## Troubleshooting

### Common Issues
1. **Upload Failures**: Check CDN API key and file size limits
2. **Permission Errors**: Verify user has correct developer/admin status
3. **Database Errors**: Ensure Prisma schema is up to date
4. **Route Access**: Check middleware configuration and authentication

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify environment variables are set correctly
3. Check server logs for API errors
4. Ensure database connection is working
5. Verify CDN API key configuration

## Future Enhancements

### Planned Features
- **App Analytics Dashboard**: Detailed analytics for developers
- **Revenue Sharing**: Paid app support with payment integration
- **App Categories Management**: Dynamic category management
- **Bulk Operations**: Bulk app management for admins
- **API Rate Limiting**: Enhanced security with rate limiting
- **App Store SEO**: Search engine optimization for public pages

### Integration Opportunities
- **CI/CD Integration**: Automated app deployment
- **Testing Framework**: Automated app testing
- **Notification System**: Real-time notifications for developers
- **Social Features**: App sharing and social integration
- **Mobile App**: Native mobile app for the store

## Support

For technical support or questions about the Developer Console system:
1. Check this documentation first
2. Review the code comments for implementation details
3. Check the GitHub issues for known problems
4. Contact the development team for assistance

## License

This Developer Console system is part of the AvehubV1 project and follows the same licensing terms as the main application.
