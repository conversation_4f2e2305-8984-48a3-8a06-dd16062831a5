#!/bin/bash

# Setup script for Next.js development environment
set -e

echo "🚀 Setting up Next.js development environment..."

# Update package lists
sudo apt-get update

# Install Node.js 20 (LTS) - check if already installed
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js 20..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
else
    echo "✅ Node.js already installed: $(node --version)"
fi

# Install pnpm using npm with sudo
echo "📦 Installing pnpm..."
sudo npm install -g pnpm

# Verify installations
echo "✅ Verifying installations..."
node --version
npm --version
pnpm --version

# Navigate to workspace and install dependencies
cd /mnt/persist/workspace

echo "📦 Installing project dependencies..."
pnpm install

# Generate Prisma client
echo "🔧 Generating Prisma client..."
pnpm exec prisma generate

echo "✅ Setup complete!"