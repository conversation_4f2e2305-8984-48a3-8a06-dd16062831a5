# Download API Integration Documentation

This document describes the integration between the AveHub apps system and the external Developer Portal API for handling app downloads.

## Overview

The download functionality has been updated to integrate with the AveHub Developer Portal API at `https://developer.avehubs.com/api` as specified in the `api.md` documentation.

## Architecture

### Download Flow

1. **User clicks download button** on app details page
2. **Frontend calls local API** (`/api/apps/[id]/download`)
3. **Local API validates app** in database (exists, approved status)
4. **Local API calls external API** (`GET /app/download/{appId}`) to get download information
5. **Local API increments download count** in local database
6. **Frontend receives download URLs** and initiates download
7. **Browser downloads file** using direct URL from external API

### API Endpoints

#### Local API Route: `/api/apps/[id]/download`
- **Method**: POST
- **Purpose**: Track downloads locally and get download info from external API
- **Authentication**: Session-based (internal)
- **Response**: Download information with URLs

#### External API Route: `https://developer.avehubs.com/api/app/download/{appId}`
- **Method**: GET
- **Purpose**: Get download URLs and metadata
- **Authentication**: Bearer token (API key)
- **Response**: Download URLs, file info, and metadata

## Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# Developer API Configuration
DEVELOPER_API_KEY="AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI"
```

### API Key

The API key is configured in the download route and can be overridden via environment variables. The default key from the API documentation is used as fallback.

## Implementation Details

### Backend (`src/app/api/apps/[id]/download/route.ts`)

```typescript
// Key features:
- Validates app exists and is approved locally
- Calls external API with proper authentication
- Handles external API errors gracefully
- Increments local download count
- Returns structured response with download URLs
- Provides fallback error handling
```

### Frontend (`src/app/apps/[id]/page.tsx`)

```typescript
// Key features:
- Calls local download API
- Handles different response types
- Creates download links dynamically
- Updates UI with new download count
- Provides comprehensive error handling
- Shows appropriate user feedback
```

### Type Definitions (`src/types/app.ts`)

```typescript
// Updated DownloadResponse interface to match API spec:
interface DownloadResponse {
  success: boolean;
  app: { id: string; name: string; version: string; };
  download: {
    directUrl: string;
    streamingUrl: string;
    fileName: string;
    contentType: string;
    supportsRangeRequests: boolean;
  };
  downloads: number;
  externalDownloads?: number;
  timestamp: string;
}
```

## Error Handling

### Local API Errors
- **404**: App not found in local database
- **403**: App not approved for download
- **500**: Internal server error

### External API Errors
- **404**: App not found on download server
- **403**: App not available for download
- **503**: External service unavailable (with fallback)

### Frontend Error Handling
- Network errors
- Invalid responses
- Missing download URLs
- Service unavailability

## Download Methods

### Direct Download
- Uses `directUrl` from external API response
- Creates temporary download link
- Triggers browser download with proper filename

### Streaming Download (Fallback)
- Uses `streamingUrl` when direct URL unavailable
- Opens in new tab/window
- Handled by external service

## Security Considerations

1. **API Key Protection**: Stored server-side only
2. **Local Validation**: Apps validated locally before external API call
3. **Error Information**: Limited error details exposed to frontend
4. **Rate Limiting**: Handled by external API
5. **Authentication**: External API requires valid API key

## Testing

### Build Verification
```bash
npm run build
```
✅ Build completes successfully with no TypeScript errors

### Manual Testing Checklist
- [ ] Download button appears for approved apps
- [ ] Download button disabled for non-approved apps
- [ ] Download count increments after successful download
- [ ] Error messages display appropriately
- [ ] File downloads with correct filename
- [ ] Fallback URLs work when direct URL unavailable

## Monitoring

### Logs to Monitor
- External API call failures
- Download count mismatches
- Authentication errors
- Network timeouts

### Metrics to Track
- Download success rate
- External API response times
- Error rates by type
- Download count accuracy

## Future Enhancements

1. **Caching**: Cache download URLs for short periods
2. **Analytics**: Track download patterns and popular apps
3. **Resume Support**: Implement resumable downloads using range requests
4. **Offline Support**: Handle offline scenarios gracefully
5. **Progress Tracking**: Show download progress for large files

## Troubleshooting

### Common Issues

1. **"Download service temporarily unavailable"**
   - External API is down or unreachable
   - Check API key configuration
   - Verify network connectivity

2. **"App not found on download server"**
   - App exists locally but not on external service
   - Sync issue between local and external databases

3. **"App not available for download"**
   - App status changed on external service
   - Permissions or approval status mismatch

### Debug Steps

1. Check environment variables
2. Verify API key validity
3. Test external API directly
4. Check local app status
5. Review server logs
