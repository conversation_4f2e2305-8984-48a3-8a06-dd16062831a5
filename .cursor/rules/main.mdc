---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
# Project Development Guidelines: Avehub 
# Website: www.avehubs.com

## 1. Core Technologies & Setup

* **Framework:** Next.js (App Router)
* **Language:** TypeScript (Strict mode enabled in `tsconfig.json`)
* **Styling:** Tailwind CSS
* **Hosting:** Vercel
* **Package Manager:** `pnpm` (Use `pnpm install`, `pnpm add`, etc.)
* **Environment:** Ensure Node.js (specify version, e.g., LTS) and `pnpm` are installed.

## 2. Project Structure

* **`src/app/`**: Main application directory using the Next.js App Router.
    * `layout.tsx`: Root layout.
    * `page.tsx`: Home page.
    * `[folder]/page.tsx`: Entry point for a specific route segment.
    * `[folder]/layout.tsx`: Layout specific to a route segment.
    * `loading.tsx`, `error.tsx`, `not-found.tsx`: Special files for UI states within segments.
    * **`[folder]/components/`**: **Page-Specific Components.** Components used *only* within the `src/app/[folder]/...` route segment and its children.
* **`src/app/api/`**: **Backend API Routes.** Contains all backend API endpoints (Route Handlers). Logic here should handle requests, interact with services/database, and return responses.
* **`src/components/`**: **Shared Components.** Reusable React components used across multiple pages/features.
    * **`src/components/ui/`**: **UI Primitives.** Generic, reusable UI elements (e.g., Button, Input, Card, Dialog). Often based on libraries like Shadcn/ui, Radix UI, etc. Should be highly composable and styleable via Tailwind.
    * **`src/components/animations/`**: Reusable animation components or hooks (e.g., using Framer Motion).
* **`src/hooks/`**: **Custom React Hooks.** Contains reusable client-side (`'use client'`) and server-side hooks (though server-side logic often lives better in `lib` or API routes).
* **`src/lib/`**: **Core Logic & Services.** Non-React specific code. Examples: database interaction functions, third-party API clients, complex business logic, utility functions closely tied to core features.
* **`src/utils/`**: **General Utilities.** Generic helper functions not specific to React or core business logic (e.g., date formatting, string manipulation, validators).
* **`src/types/`**: **Shared Types.** Contains TypeScript type definitions and interfaces used across the application (frontend and backend).
* **`src/middleware.ts`**: **Edge Middleware.** Executes before requests reach API routes or pages. Used for tasks like authentication checks, redirects, A/B testing logic, header modifications. Should be lightweight.
* **`src/auth.ts`**: **Authentication Configuration.** Central place for authentication setup (e.g., NextAuth.js configuration, providers, callbacks, session management logic).
* **`src/routes.ts`**: **Route Definitions.** Often used to define public, private, and authentication-related routes, which can be imported by `middleware.ts` or `auth.ts` for logic checks.

## 3. Coding Standards & Conventions

* **TypeScript:**
    * Enable and respect `strict` mode in `tsconfig.json`.
    * Avoid `any` type whenever possible; use specific types, `unknown`, or generics.
    * Utilize types/interfaces defined in `src/types/` for shared data structures.
    * Use explicit return types for functions.
* **Naming:**
    * Components: `PascalCase` (e.g., `UserProfile.tsx`)
    * Files (non-components): `kebab-case` (e.g., `api-client.ts`, `date-utils.ts`)
    * Functions/Variables: `camelCase` (e.g., `getUserData`)
    * Types/Interfaces: `PascalCase` (e.g., `interface UserProfileData`)
    * Constants: `UPPER_SNAKE_CASE` (e.g., `MAX_RETRIES`)
* **Tailwind CSS:**
    * Leverage theme configuration in `tailwind.config.ts` for consistent spacing, colors, fonts, etc.
    * Avoid arbitrary values (`w-[123px]`) unless absolutely necessary; prefer theme values (`w-32`).
    * Group related utility classes logically (e.g., layout, typography, colors, interactions). Consider using Tailwind Prettier plugins for automatic sorting.
* **Code Style:**
    * Use Prettier for code formatting. Ensure `.prettierrc` is configured and used. Run `pnpm format` before committing.
    * Use ESLint for code linting. Ensure `.eslintrc.js` (or similar) is configured with relevant plugins (React, TypeScript, Next.js, Tailwind). Run `pnpm lint` before committing. Address all errors and warnings.

## 4. Development Workflow & Quality Assurance

* **Branching:** Use a standard Git flow (e.g., Gitflow: `main`, `develop`, `feature/`, `fix/`, `chore/`). Create feature branches from `develop`.
* **Commits:** Follow the Conventional Commits specification (e.g., `feat: add user login`, `fix: correct button alignment`, `refactor: improve api error handling`, `test: add unit tests for utils`).
* **Testing:**
    * **Unit Tests:** Mandatory for functions in `src/lib`, `src/utils`, complex hooks, and core API route logic. Use a testing framework like Jest or Vitest with React Testing Library for components/hooks. Place test files alongside the code they test (e.g., `utils.test.ts` next to `utils.ts`). Run `pnpm test`.
    * **(Optional) Integration Tests:** Consider for flows involving multiple components or API interactions.
    * **(Optional) E2E Tests:** Consider for critical user paths (e.g., using Playwright or Cypress).
* **Build Verification:**
    * Run `pnpm build` frequently, and **always before pushing or creating a Pull Request.** The build **must succeed** without any TypeScript or ESLint errors.
    * Run `pnpm tsc --noEmit` to check types independently.
* **Code Reviews:** All code merged into `develop` or `main` must be reviewed via Pull Requests. Reviews should check for correctness, adherence to guidelines, performance, security, and test coverage.

## 5. Backend (API) Development (`src/app/api/`)

* Use Next.js Route Handlers.
* Keep handlers focused: receive request, validate input (e.g., using Zod), call relevant functions/services from `src/lib`, format response.
* Implement consistent error handling and response structures (e.g., `{ data: ... }` on success, `{ error: { message: ... } }` on failure with appropriate HTTP status codes).
* Secure endpoints appropriately, often by checking sessions/tokens (leveraging `src/auth.ts` and potentially `src/middleware.ts`).

## 6. Frontend Development

* Prioritize React Server Components (RSCs) for performance. Use Client Components (`'use client'`) only when interactivity or browser-specific APIs are needed.
* Fetch data in Server Components where possible, or use Route Handlers (`src/app/api/`) called from Client Components.
* Build UIs compositionally using elements from `src/components/ui/` and shared components from `src/components/`.
* **Component Usage:** Before importing and using a component (especially from `src/components/` or `src/components/ui/`), verify it exists. If it doesn't, create it first, following the project structure guidelines (e.g., place reusable UI primitives in `ui`, shared components in `components`, page-specific ones in `app/[folder]/components/`).
* Ensure Accessibility (a11y): Use semantic HTML, ARIA attributes where necessary, ensure keyboard navigation and screen reader compatibility.
* Optimize Performance: Use `next/image` for image optimization, consider code splitting (handled well by App Router), and minimize Client Component bundle sizes.

## 7. Environment Variables

* Use `.env.local` for local secrets (this file should be in `.gitignore`).
* Use `.env.example` to document all required environment variables.
* Prefix variables exposed to the browser with `NEXT_PUBLIC_`. Access all variables via `process.env`.
* Configure environment variables securely in Vercel project settings for deployment.

## 8. Deployment

* Deployment is handled via Vercel, typically connected to the Git repository (`main` branch for production, potentially `develop` for staging).
* Monitor Vercel build logs and runtime function logs for errors.



