{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "prisma generate && next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "0.38.0", "@auth/prisma-adapter": "^2.8.0", "@clerk/nextjs": "^6.12.5", "@google/generative-ai": "^0.24.0", "@prisma/client": "^6.5.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-select": "^2.1.7", "@supabase/supabase-js": "^2.49.4", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "glob": "^11.0.1", "lucide-react": "^0.471.2", "multer": "1.4.5-lts.1", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "nodemailer": "^6.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^9.1.0", "react-toastify": "^11.0.5", "sonner": "^2.0.3", "swr": "2.2.5", "tailwind-merge": "^3.2.0", "vercel": "^40.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@types/hast": "^3.0.4", "@types/node": "^20.17.24", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.11", "@types/react-dom": "^19.0.4", "@types/unist": "^3.0.3", "eslint": "^9.25.1", "eslint-config-next": "15.1.0", "eslint-plugin-react": "7.37.5", "postcss": "^8.5.3", "prisma": "^6.5.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}, "packageManager": "pnpm@10.9.0+sha512.0486e394640d3c1fb3c9d43d49cf92879ff74f8516959c235308f5a8f62e2e19528a65cdc2a3058f587cde71eba3d5b56327c8c33a97e4c4051ca48a10ca2d5f"}