# Authentication and URL Fixes Documentation

This document describes the fixes implemented for the NextAuth UntrustedHost error and Appwrite storage URL double v1 redirect issue.

## Issues Fixed

### 1. NextAuth UntrustedHost Error

**Problem**: Authentication error when accessing download endpoints:
```
[auth][error] UntrustedHost: Host must be trusted. URL was: http://localhost:3000/api/auth/session
```

**Root Cause**: NextAuth was not configured to trust localhost:3000 and the middleware was incorrectly handling API authentication routes.

**Solution Implemented**:

#### A. Updated NextAuth Configuration (`src/lib/auth.config.ts`)
- Added `trustHost: true` for development environment
- Configured proper base path for auth routes
- Added explicit host trusting configuration

#### B. Fixed API Route Prefix (`src/routes.ts`)
- Changed `API_AUTH_PREFIX` from `/api/client/auth` to `/api/auth`
- Added `PUBLIC_API_ROUTES` for download endpoints that don't require authentication
- Properly categorized download routes as public

#### C. Enhanced Middleware (`src/middleware.ts`)
- Added support for public API routes
- Implemented dynamic route matching for `/api/apps/[id]/download`
- Improved route pattern matching with regex
- Added proper logging for debugging

#### D. Environment Configuration (`.env.example`)
- Added `NEXTAUTH_URL` configuration
- Added `AUTH_TRUST_HOST` setting
- Documented proper environment setup

### 2. Appwrite Storage URL Double v1 Redirect Issue

**Problem**: Download URLs getting double `/v1/` in the path:
- Initial: `https://api.avehubs.com/v1/storage/buckets/.../download`
- Redirected to: `https://api.avehubs.com/v1/v1/storage/buckets/...` (double v1)

**Root Cause**: URL construction issues in both backend and frontend processing.

**Solution Implemented**:

#### A. Backend URL Fixing (`src/app/api/apps/[id]/download/route.ts`)
- Added `fixAppwriteUrl()` utility function to detect and fix double `/v1/` patterns
- Added `processDownloadUrls()` function to process all download URLs
- Integrated URL fixing into the download response pipeline
- Added validation for proper Appwrite URL format

#### B. Frontend URL Validation (`src/app/apps/[id]/page.tsx`)
- Added frontend URL fixing as additional safety measure
- Updated screenshot URL handling to use the same fixing logic
- Enhanced download handling to validate URLs before use
- Added fallback mechanisms for different URL types

## Technical Implementation

### URL Fixing Algorithm

```typescript
function fixAppwriteUrl(url: string): string {
  if (!url) return url;
  
  // Fix double /v1/v1/ in Appwrite URLs
  if (url.includes('/v1/v1/')) {
    return url.replace('/v1/v1/', '/v1/');
  }
  
  // Ensure proper Appwrite URL format
  if (url.includes('api.avehubs.com') && !url.includes('/v1/')) {
    return url.replace('api.avehubs.com/', 'api.avehubs.com/v1/');
  }
  
  return url;
}
```

### Middleware Route Matching

```typescript
const isAccessingPublicApiRoute = PUBLIC_API_ROUTES.some(route => {
  // Handle dynamic routes like /api/apps/[id]/download
  if (route.includes('[id]')) {
    const routePattern = route.replace('[id]', '[^/]+');
    const regex = new RegExp(`^${routePattern}$`);
    return regex.test(pathname);
  }
  return pathname.startsWith(route);
});
```

## Files Modified

### Configuration Files
- `.env.example` - Added NextAuth configuration
- `src/lib/auth.config.ts` - Enhanced NextAuth settings
- `src/routes.ts` - Updated route definitions

### Middleware and Authentication
- `src/middleware.ts` - Enhanced route handling and authentication logic

### Backend API
- `src/app/api/apps/[id]/download/route.ts` - Added URL fixing and validation

### Frontend
- `src/app/apps/[id]/page.tsx` - Enhanced download handling and URL validation

## Testing Results

### Build Verification
- ✅ TypeScript compilation successful
- ✅ Next.js build completed without errors
- ✅ No authentication-related build issues
- ✅ All route configurations validated

### Authentication Flow
- ✅ NextAuth routes properly configured
- ✅ Download endpoints accessible without authentication errors
- ✅ Middleware correctly handles public API routes
- ✅ Trusted host configuration working

### URL Processing
- ✅ Double `/v1/` patterns detected and fixed
- ✅ Backend URL processing working correctly
- ✅ Frontend URL validation as additional safety
- ✅ Screenshot URLs properly handled

## Environment Setup

### Required Environment Variables

```bash
# NextAuth Configuration
NEXTAUTH_SECRET="your_nextauth_secret_here"
NEXTAUTH_URL="http://localhost:3000"
AUTH_TRUST_HOST="true"

# For production
# NEXTAUTH_URL="https://yourdomain.com"
```

### Development Setup
1. Copy `.env.example` to `.env.local`
2. Configure `NEXTAUTH_URL` for your environment
3. Set `AUTH_TRUST_HOST="true"` for development
4. Ensure Google OAuth credentials are configured

### Production Setup
1. Set `NEXTAUTH_URL` to your production domain
2. Configure proper SSL certificates
3. Ensure trusted host configuration matches your domain
4. Test authentication flow thoroughly

## Monitoring and Debugging

### Logs to Monitor
- Middleware route matching: `Middleware - Path: ${pathname}, Auth: ${!!isAuth}`
- Public API route access: `Middleware - Allowing public API route: ${pathname}`
- URL fixing operations: Check for double `/v1/` patterns in logs
- Download URL processing: Verify URLs are properly formatted

### Common Issues and Solutions

1. **"UntrustedHost" Error**
   - Check `NEXTAUTH_URL` environment variable
   - Verify `trustHost: true` in auth config
   - Ensure middleware allows auth routes

2. **Download URLs with Double v1**
   - Check URL fixing functions are working
   - Verify external API response format
   - Test URL processing in both backend and frontend

3. **Authentication Required for Downloads**
   - Verify download routes are in `PUBLIC_API_ROUTES`
   - Check middleware route matching logic
   - Ensure API auth prefix is correct

## Security Considerations

1. **Trusted Hosts**: Only trust necessary hosts in production
2. **Public Routes**: Carefully manage which routes are public
3. **URL Validation**: Always validate and sanitize URLs
4. **Error Handling**: Don't expose sensitive information in errors
5. **Logging**: Log security-relevant events for monitoring

## Future Enhancements

1. **Rate Limiting**: Add rate limiting for download endpoints
2. **URL Caching**: Cache validated URLs to improve performance
3. **Health Checks**: Add health checks for external API dependencies
4. **Metrics**: Track authentication and download success rates
5. **Alerting**: Set up alerts for authentication failures
