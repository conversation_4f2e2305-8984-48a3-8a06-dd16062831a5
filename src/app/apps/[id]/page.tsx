/*
 * Official Avehub Code, verified
 * App Detail Page - Individual app information and download
 * Any unauthorized modifications will invalidate service warranty
 */

"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { Header } from "@/components/Header";
import Footer from "@/components/Footer";
import { motion } from "framer-motion";
import {
  Download,
  Star,
  MessageSquare,
  Calendar,
  Package,
  ExternalLink,
  Mail,
  Globe,
  Tag,
  User,
  ArrowLeft,
  Send,
  AlertTriangle,
  Ban,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { toast } from "sonner";
import {
  App,
  AppComment,
  AppVersion,
  CreateCommentData,
  DownloadResponse
} from "@/types/app";
import { AppIcon } from "@/components/apps/AppIcon";
import { AppStats } from "@/components/apps/AppStats";
import { LoadingState } from "@/components/apps/LoadingState";
import { ErrorState } from "@/components/apps/ErrorState";

export default function AppDetail() {
  const params = useParams();
  const { data: session } = useSession();
  const [app, setApp] = useState<App | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [downloading, setDownloading] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [newRating, setNewRating] = useState(0);
  const [submittingComment, setSubmittingComment] = useState(false);

  // [2] Fetch app data
  useEffect(() => {
    if (params.id) {
      fetchApp();
    }
  }, [params.id]);

  const fetchApp = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`/api/apps/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setApp(data);
      } else if (response.status === 404) {
        setError("App not found");
        toast.error("App not found");
      } else {
        setError("Failed to load app");
        toast.error("Failed to load app");
      }
    } catch (error) {
      console.error("Error fetching app:", error);
      setError("Failed to load app");
      toast.error("Failed to load app");
    } finally {
      setLoading(false);
    }
  };

  // [3] Retry function
  const handleRetry = () => {
    fetchApp();
  };

  // [4] Handle download
  const handleDownload = async () => {
    if (!app) return;

    setDownloading(true);
    try {
      // Get download information from our API (which calls the external API)
      const response = await fetch(`/api/apps/${app.id}/download`, {
        method: "POST"
      });

      if (response.ok) {
        const downloadData = await response.json();

        // Update download count immediately
        setApp(prev => prev ? { ...prev, downloads: downloadData.downloads } : null);

        if (downloadData.success && downloadData.download) {
          // Use the direct download URL from the external API (already fixed by backend)
          const downloadUrl = downloadData.download.directUrl;
          const streamingUrl = downloadData.download.streamingUrl;

          if (downloadUrl) {
            // Double-check and fix URL on frontend as well
            const fixedDownloadUrl = fixAppwriteUrl(downloadUrl);

            // Create a temporary link to trigger download
            const link = document.createElement('a');
            link.href = fixedDownloadUrl;
            link.download = downloadData.download.fileName || `${app.name}_${app.version}.zip`;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast.success("Download started!");
          } else if (streamingUrl) {
            // Fallback: open the streaming URL
            const fixedStreamingUrl = fixAppwriteUrl(streamingUrl);
            window.open(fixedStreamingUrl, '_blank');
            toast.success("Download started!");
          } else {
            toast.error("Download URLs not available");
          }
        } else {
          toast.error("Download information not available");
        }
      } else if (response.status === 404) {
        toast.error("App not found");
      } else if (response.status === 403) {
        toast.error("App is not available for download");
      } else if (response.status === 503) {
        const errorData = await response.json();
        toast.error(errorData.error || "Download service temporarily unavailable");
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Download failed");
      }
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Download failed. Please check your connection and try again.");
    } finally {
      setDownloading(false);
    }
  };

  // [5] Submit comment
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!session) {
      toast.error("Please login to comment");
      return;
    }

    if (!newComment.trim()) {
      toast.error("Please enter a comment");
      return;
    }

    setSubmittingComment(true);
    try {
      const commentData: CreateCommentData = {
        content: newComment.trim(),
        rating: newRating || undefined
      };

      const response = await fetch(`/api/apps/${app?.id}/comments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(commentData)
      });

      if (response.ok) {
        const comment: AppComment = await response.json();
        setApp(prev => prev ? {
          ...prev,
          comments: [comment, ...prev.comments],
          _count: { comments: prev._count.comments + 1 }
        } : null);
        setNewComment("");
        setNewRating(0);
        toast.success("Comment added!");
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to add comment");
      }
    } catch (error) {
      console.error("Comment error:", error);
      toast.error("Failed to add comment");
    } finally {
      setSubmittingComment(false);
    }
  };

  // [6] Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // [7] Format downloads
  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K`;
    }
    return downloads.toString();
  };

  // [8] Utility function to fix double v1 in URLs
  const fixAppwriteUrl = (url: string): string => {
    if (!url) return url;

    // Fix double /v1/v1/ in Appwrite URLs
    if (url.includes('/v1/v1/')) {
      return url.replace('/v1/v1/', '/v1/');
    }

    return url;
  };

  // [9] Get screenshot URL with proper handling for api.avehubs.com only
  const getScreenshotUrl = (screenshotUrl: string) => {
    // If it's already a full URL and from api.avehubs.com, use it
    if (screenshotUrl.startsWith('https://api.avehubs.com')) {
      return fixAppwriteUrl(screenshotUrl);
    }

    // If it's already a full URL but not from api.avehubs.com, return null
    if (screenshotUrl.startsWith('http')) {
      return null;
    }

    // For relative paths or file IDs, we can't construct the URL without more context
    // Return null to hide the screenshot
    return null;
  };

  if (loading) {
    return (
      <>
        <Header />
        <LoadingState type="detail" />
      </>
    );
  }

  if (error || !app) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <ErrorState
            type={error === "App not found" ? "not-found" : "error"}
            title={error === "App not found" ? "App Not Found" : "Failed to Load App"}
            message={error === "App not found"
              ? "The app you're looking for doesn't exist or has been removed."
              : error || "Something went wrong while loading the app."
            }
            onRetry={error !== "App not found" ? handleRetry : undefined}
            showBackButton={true}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* [7] Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-6"
        >
          <Link
            href="/apps"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Apps
          </Link>
        </motion.div>

        {/* [8] App Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm mb-8"
        >
          <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
            {/* App Icon */}
            <div className="flex-shrink-0 mb-6 lg:mb-0">
              <AppIcon
                iconUrl={app.iconUrl}
                appName={app.name}
                size="xl"
                className="rounded-2xl"
              />
            </div>

            {/* App Info */}
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {app.name}
              </h1>

              <div className="flex items-center space-x-4 mb-4 text-gray-600 dark:text-gray-400">
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  <span>{app.developer.name}</span>
                </div>
                <div className="flex items-center">
                  <Tag className="w-4 h-4 mr-1" />
                  <span>{app.category}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>v{app.version}</span>
                </div>
              </div>

              <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                {app.description}
              </p>

              {/* Stats */}
              <div className="flex items-center space-x-6 mb-6 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center">
                  <Download className="w-4 h-4 mr-1" />
                  <span>{formatDownloads(app.downloads)} downloads</span>
                </div>
                <div className="flex items-center">
                  <MessageSquare className="w-4 h-4 mr-1" />
                  <span>{app._count.comments} comments</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>Updated {formatDate(app.updatedAt)}</span>
                </div>
              </div>

              {/* Download Button or Suspension Notice */}
              {app.status === "SUSPENDED" ? (
                <div className="inline-flex items-center px-6 py-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg font-medium">
                  <Ban className="w-5 h-5 mr-2" />
                  App is suspended
                </div>
              ) : app.status === "PENDING" ? (
                <div className="inline-flex items-center px-6 py-3 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-lg font-medium">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  App is pending approval
                </div>
              ) : app.status === "REJECTED" ? (
                <div className="inline-flex items-center px-6 py-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg font-medium">
                  <Ban className="w-5 h-5 mr-2" />
                  App has been rejected
                </div>
              ) : (
                <button
                  onClick={handleDownload}
                  disabled={downloading}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
                >
                  {downloading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Downloading...
                    </>
                  ) : (
                    <>
                      <Download className="w-5 h-5 mr-2" />
                      Download
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </motion.div>

        {/* [9] Screenshots */}
        {(() => {
          const validScreenshots = app.screenshots
            .map((screenshot, index) => ({
              url: getScreenshotUrl(screenshot),
              index,
              original: screenshot
            }))
            .filter(item => item.url !== null);

          return validScreenshots.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm mb-8"
            >
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Screenshots</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {validScreenshots.map((item) => (
                  <div key={item.index} className="relative">
                    <Image
                      src={item.url!}
                      alt={`${app.name} screenshot ${item.index + 1}`}
                      width={400}
                      height={300}
                      className="rounded-lg object-cover w-full h-48"
                      onError={(e) => {
                        // Hide broken images
                        const target = e.target as HTMLImageElement;
                        target.parentElement?.classList.add('hidden');
                      }}
                    />
                  </div>
                ))}
              </div>
            </motion.div>
          );
        })()}

        {/* [10] Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8"
        >
          {/* App Details */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Details</h2>

            <div className="space-y-4">
              {app.tags.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {app.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {app.changelog && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">What's New</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                    {app.changelog}
                  </p>
                </div>
              )}

              {(app.minVersion || app.maxVersion) && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">System Requirements</h3>
                  <div className="text-gray-600 dark:text-gray-400 text-sm">
                    {app.minVersion && <p>Minimum version: {app.minVersion}</p>}
                    {app.maxVersion && <p>Maximum version: {app.maxVersion}</p>}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Developer Info */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Developer</h2>

            <div className="flex items-center space-x-3 mb-4">
              {app.developer.image ? (
                <Image
                  src={app.developer.image}
                  alt={app.developer.name}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
              ) : (
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-gray-500" />
                </div>
              )}
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">{app.developer.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Developer</p>
              </div>
            </div>

            <div className="space-y-3">
              {app.website && (
                <a
                  href={app.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  Website
                  <ExternalLink className="w-3 h-3 ml-1" />
                </a>
              )}

              {app.supportEmail && (
                <a
                  href={`mailto:${app.supportEmail}`}
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Support
                </a>
              )}
            </div>
          </div>
        </motion.div>

        {/* [11] Comments Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm"
        >
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
            Comments ({app._count.comments})
          </h2>

          {/* Comment Form */}
          {session && app.status === "APPROVED" ? (
            <form onSubmit={handleSubmitComment} className="mb-8">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Rating (optional)
                </label>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setNewRating(star)}
                      className={`w-6 h-6 ${
                        star <= newRating ? "text-yellow-400" : "text-gray-300"
                      }`}
                    >
                      <Star className="w-full h-full fill-current" />
                    </button>
                  ))}
                </div>
              </div>

              <div className="mb-4">
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Write your comment..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <button
                type="submit"
                disabled={submittingComment || !newComment.trim()}
                className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors"
              >
                {submittingComment ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Posting...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Post Comment
                  </>
                )}
              </button>
            </form>
          ) : (
            <div className="mb-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg text-center">
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                {!session
                  ? "Please login to leave a comment"
                  : app.status !== "APPROVED"
                    ? "Comments are disabled for this app"
                    : "Please login to leave a comment"
                }
              </p>
            </div>
          )}

          {/* Comments List */}
          <div className="space-y-6">
            {app.comments.length === 0 ? (
              <p className="text-gray-600 dark:text-gray-400 text-center py-8">
                No comments yet. Be the first to comment!
              </p>
            ) : (
              app.comments.map((comment) => (
                <div key={comment.id} className="border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0">
                  <div className="flex items-start space-x-3">
                    {comment.user.image ? (
                      <Image
                        src={comment.user.image}
                        alt={comment.user.name}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-gray-500" />
                      </div>
                    )}

                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {comment.user.name}
                        </h4>
                        {comment.rating && (
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < comment.rating! ? "text-yellow-400 fill-current" : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                        )}
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {formatDate(comment.createdAt)}
                        </span>
                      </div>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {comment.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </motion.div>
      </div>

      <Footer />
    </div>

  );
}
