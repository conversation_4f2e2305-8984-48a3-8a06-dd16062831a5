'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Play, RotateCcw, Ghost, WavesLadder } from 'lucide-react';
import Link from 'next/link';

type Bubble = {
  id: number;
  size: number;
  left: number;
  duration: number;
  opacity: number;
  drift: number;
};

type PoppingBubble = {
  id: number;
  size: number;
  x: number;
  y: number;
};

export default function NotFound() {
  const [isClient, setIsClient] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [score, setScore] = useState(0);

  const [bgBubbles, setBgBubbles] = useState<Bubble[]>([]);
  const [bubbles, setBubbles] = useState<Bubble[]>([]);
  const [poppingBubbles, setPoppingBubbles] = useState<PoppingBubble[]>([]);

  const nextIdRef = useRef(0);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const popSoundsRef = useRef<HTMLAudioElement[]>([]);

  useEffect(() => {
    setIsClient(true);
    if (typeof Audio !== 'undefined' && popSoundsRef.current.length === 0) {
      const files = ['pop1.mp3', 'pop2.mp3', 'pop3.mp3'];
      popSoundsRef.current = files.map((f) => {
        const audio = new Audio(`/sounds/${f}`);
        audio.volume = 0.5;
        audio.load();
        return audio;
      });
    }
  }, []);

  const createBubble = useCallback((id: number): Bubble => {
    return {
      id,
      size: 10 + Math.random() * 30,
      left: Math.random() * 100,
      duration: 4 + Math.random() * 4,
      opacity: 0.3 + Math.random() * 0.4,
      drift: (Math.random() - 0.5) * 20,
    };
  }, []);

  useEffect(() => {
    if (!isClient) return;
    const initialCount = 8;
    const list: Bubble[] = [];
    for (let i = 0; i < initialCount; i++) {
      list.push(createBubble(nextIdRef.current++));
    }
    setBgBubbles(list);
  }, [isClient, createBubble]);

  const handleBubbleReachTop = useCallback(
    (id: number) => {
      if (!gameStarted || gameOver) return;
      setGameOver(true);
      setGameStarted(false);
      setBubbles([]);
    },
    [gameStarted, gameOver]
  );

  const popBubble = useCallback(
    (bubble: Bubble, e: React.MouseEvent<HTMLDivElement>) => {
      if (!gameStarted || gameOver) return;
      if (popSoundsRef.current.length) {
        const idx = Math.floor(Math.random() * popSoundsRef.current.length);
        const snd = popSoundsRef.current[idx];
        snd.currentTime = 0;
        snd.play().catch(() => {});
      }
      const containerRect = containerRef.current?.getBoundingClientRect();
      const bubbleRect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      if (!containerRect) return;
      const xPos = bubbleRect.left - containerRect.left + bubbleRect.width / 2;
      const yPos = bubbleRect.top - containerRect.top + bubbleRect.height / 2;
      setBubbles((prev) => prev.filter((b) => b.id !== bubble.id));
      setPoppingBubbles((prev) => [
        ...prev,
        { id: bubble.id, size: bubble.size, x: xPos, y: yPos },
      ]);
      setScore((s) => s + 1);
    },
    [gameStarted, gameOver]
  );

  useEffect(() => {
    if (!isClient || !gameStarted || gameOver) return;
    if (bubbles.length >= 7) return;
    const delay = 800 + Math.random() * 800;
    const timeout = setTimeout(() => {
      const raw = 1 + Math.floor(Math.random() * 3);
      const spaceLeft = Math.max(0, 7 - bubbles.length);
      const toSpawn = Math.min(raw, spaceLeft);
      if (toSpawn > 0) {
        setBubbles((prev) => {
          const newList = [...prev];
          for (let i = 0; i < toSpawn; i++) {
            const id = nextIdRef.current++;
            newList.push(createBubble(id));
          }
          return newList;
        });
      }
    }, delay);
    return () => clearTimeout(timeout);
  }, [bubbles, isClient, gameStarted, gameOver, createBubble]);

  const startGame = useCallback(() => {
    setGameStarted(true);
    setBgBubbles([]);
    setGameOver(false);
    setScore(0);
    setBubbles(() => {
      const initial: Bubble[] = [];
      for (let i = 0; i < 5; i++) {
        initial.push(createBubble(nextIdRef.current++));
      }
      return initial;
    });
  }, [createBubble]);

  return (
    <div
      ref={containerRef}
      className="relative h-screen w-full bg-blue-950 overflow-hidden flex items-center justify-center text-white"
    >
      <div className="h-[500px] w-[500px] rounded-full bg-blue-500 blur-[150px] opacity-50 absolute top-[-100px] left-1/2 transform -translate-x-1/2 pointer-events-none" />

      {!gameStarted && !gameOver && (
        <button
          onClick={startGame}
          className="absolute top-4 right-4 z-20 inline-flex items-center gap-2 bg-blue-700 hover:bg-blue-600 transition-colors text-white px-5 py-2 rounded-full shadow-md"
        >
          <Play className="w-4 h-4" />
          Start Game
        </button>
      )}

      {!gameStarted &&
        !gameOver &&
        isClient &&
        bgBubbles.map((bubble) => (
          <motion.div
            key={bubble.id}
            className="absolute rounded-full bg-blue-300"
            style={{
              width: `${bubble.size}px`,
              height: `${bubble.size}px`,
              left: `${bubble.left}%`,
              opacity: bubble.opacity,
              bottom: `-${bubble.size}px`,
            }}
            initial={{ y: 0, x: bubble.drift }}
            animate={{ y: `-110vh`, x: bubble.drift }}
            transition={{
              duration: bubble.duration,
              ease: 'linear',
              repeat: Infinity,
              repeatDelay: 0.5 + Math.random() * 0.5,
            }}
          />
        ))}

      {!gameStarted && !gameOver && (
        <motion.div
          initial={{ y: -10 }}
          animate={{ y: 10 }}
          transition={{
            duration: 3,
            repeat: Infinity,
            repeatType: 'reverse',
            ease: 'easeInOut',
          }}
          className="z-10 text-center pointer-events-none select-none"
        >
          <div className="text-7xl font-bold tracking-wider text-blue-200 drop-shadow-lg">
            404
          </div>
          <div className="text-lg text-blue-300 flex justify-center items-center gap-2 mt-2">
            <Ghost className="w-5 h-5" />
            Page sunk deep in the ocean...
          </div>
          <div className="mt-6 pointer-events-auto">
            <Link
              href="/"
              className="inline-flex items-center gap-2 bg-blue-700 hover:bg-blue-600 px-6 py-3 rounded-full text-lg font-semibold shadow-lg"
            >
              <WavesLadder className="w-5 h-5" />
              Swim Back Home
            </Link>
          </div>
        </motion.div>
      )}

      {gameStarted && (
        <div className="absolute top-4 right-4 text-lg bg-blue-800 px-4 py-2 rounded-full shadow-lg z-10">
          Score: {score}
        </div>
      )}

      {gameOver && (
        <div className="z-10 text-center">
          <div className="text-5xl font-bold mb-4">Game Over</div>
          <div className="text-2xl mb-6">Score: {score}</div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button
              onClick={() => {
                setGameOver(false);
                startGame();
              }}
              className="inline-flex items-center gap-2 bg-blue-700 hover:bg-blue-600 px-6 py-3 rounded-full text-lg font-semibold shadow-lg"
            >
              <RotateCcw className="w-5 h-5" />
              Restart
            </button>

            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center gap-2 bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-full text-lg font-semibold shadow-lg"
            >
              <ArrowLeft className="w-5 h-5" />
              Back
            </button>
          </div>
        </div>
      )}

      {isClient &&
        gameStarted &&
        bubbles.map((bubble) => (
          <motion.div
            key={bubble.id}
            className="absolute rounded-full bg-blue-300 cursor-pointer"
            style={{
              width: `${bubble.size}px`,
              height: `${bubble.size}px`,
              left: `${bubble.left}%`,
              opacity: bubble.opacity,
              bottom: `-${bubble.size}px`,
            }}
            initial={{ y: 0, x: bubble.drift }}
            animate={{ y: `-110vh`, x: bubble.drift }}
            transition={{
              duration: bubble.duration,
              ease: 'linear',
            }}
            onClick={(e) => popBubble(bubble, e)}
            onAnimationComplete={() => handleBubbleReachTop(bubble.id)}
          />
        ))}

      <AnimatePresence>
        {poppingBubbles.map((pop) => (
          <motion.div
            key={`pop-${pop.id}-${pop.x.toFixed(0)}-${pop.y.toFixed(0)}`}
            className="absolute rounded-full bg-blue-200 border border-white"
            style={{
              width: `${pop.size}px`,
              height: `${pop.size}px`,
              left: pop.x - pop.size / 2,
              top: pop.y - pop.size / 2,
            }}
            initial={{ scale: 1, opacity: 1 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
            exit={{ opacity: 0 }}
            onAnimationComplete={() => {
              setPoppingBubbles((prev) =>
                prev.filter((p) => p.id !== pop.id)
              );
            }}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}
