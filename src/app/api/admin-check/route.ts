import { NextResponse } from "next/server";
import db from "@/lib/db";

export async function POST(req: Request) {
    try {
        // Check if request body is empty
        const text = await req.text();
        if (!text) {
            return NextResponse.json({ error: "Empty request body" }, { status: 400 });
        }

        // Parse JSON safely
        let data;
        try {
            data = JSON.parse(text);
        } catch (e) {
            return NextResponse.json({ error: "Invalid JSON" }, { status: 400 });
        }

        const { email } = data;
        
        if (!email) {
            return NextResponse.json({ error: "Email is required" }, { status: 400 });
        }

        const user = await db.user.findUnique({
            where: { email },
            select: { admin: true },
        });

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        // Explicitly convert to boolean to avoid type issues
        const isAdmin = user.admin === true;
        console.log(`API admin-check: Email: ${email}, Raw admin value: ${user.admin}, Interpreted as: ${isAdmin}`);
        
        return NextResponse.json({ admin: isAdmin });
    } catch (error) {
        console.error("Admin check error:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}
