/*
 * Official Avehub Code, verified
 * App Download API Route - Handles download tracking
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

// [1] POST - Track app download
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // [1.1] Check if app exists and is approved
    const app = await prisma.app.findUnique({
      where: { id },
      select: {
        id: true,
        downloadUrl: true,
        status: true,
        downloads: true
      }
    });

    if (!app) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    if (app.status !== "APPROVED") {
      return NextResponse.json({ error: "App is not available for download" }, { status: 403 });
    }

    // [1.2] Increment download count
    await prisma.app.update({
      where: { id },
      data: {
        downloads: {
          increment: 1
        }
      }
    });

    // [1.3] Return proxy download URL (extract file ID from CDN URL)
    let proxyUrl = app.downloadUrl;

    // If it's a CDN URL, convert to proxy URL
    if (app.downloadUrl && app.downloadUrl.includes('cdn.avehubs.com/f/')) {
      const fileId = app.downloadUrl.split('/f/')[1];
      proxyUrl = `/api/download/${fileId}`;
    }

    return NextResponse.json({
      downloadUrl: proxyUrl,
      downloads: app.downloads + 1
    });
  } catch (error) {
    console.error("Error tracking download:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
