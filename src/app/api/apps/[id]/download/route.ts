/*
 * Official Avehub Code, verified
 * App Download API Route - Handles download tracking and external API integration
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

// External API configuration
const DEVELOPER_API_BASE_URL = "https://developer.avehubs.com/api";
const DEVELOPER_API_KEY = process.env.DEVELOPER_API_KEY || "AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI";

// [0] Utility function to fix double v1 in URLs
function fixAppwriteUrl(url: string): string {
  if (!url) return url;

  // Fix double /v1/v1/ in Appwrite URLs
  if (url.includes('/v1/v1/')) {
    return url.replace('/v1/v1/', '/v1/');
  }

  // Ensure proper Appwrite URL format
  if (url.includes('api.avehubs.com') && !url.includes('/v1/')) {
    // If it's an api.avehubs.com URL but missing /v1/, add it
    return url.replace('api.avehubs.com/', 'api.avehubs.com/v1/');
  }

  return url;
}

// [0.1] Utility function to validate and fix download URLs
function processDownloadUrls(downloadInfo: any): any {
  if (!downloadInfo || !downloadInfo.download) {
    return downloadInfo;
  }

  const download = downloadInfo.download;

  // Fix URLs if they exist
  if (download.directUrl) {
    download.directUrl = fixAppwriteUrl(download.directUrl);
  }

  if (download.streamingUrl) {
    download.streamingUrl = fixAppwriteUrl(download.streamingUrl);
  }

  return downloadInfo;
}

// [1] POST - Track app download and get download info from external API
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // [1.1] Check if app exists and is approved in local database
    const app = await prisma.app.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        status: true,
        downloads: true,
        version: true
      }
    });

    if (!app) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    if (app.status !== "APPROVED") {
      return NextResponse.json({ error: "App is not available for download" }, { status: 403 });
    }

    // [1.2] Get download information from external API
    try {
      const externalResponse = await fetch(`${DEVELOPER_API_BASE_URL}/app/download/${id}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${DEVELOPER_API_KEY}`,
          "Content-Type": "application/json"
        }
      });

      if (!externalResponse.ok) {
        if (externalResponse.status === 404) {
          return NextResponse.json({ error: "App not found on download server" }, { status: 404 });
        }
        if (externalResponse.status === 403) {
          return NextResponse.json({ error: "App not available for download" }, { status: 403 });
        }
        throw new Error(`External API error: ${externalResponse.status}`);
      }

      const downloadInfo = await externalResponse.json();

      // [1.2.1] Process and fix download URLs
      const processedDownloadInfo = processDownloadUrls(downloadInfo);

      // [1.3] Increment local download count
      const updatedApp = await prisma.app.update({
        where: { id },
        data: {
          downloads: {
            increment: 1
          }
        },
        select: {
          downloads: true
        }
      });

      // [1.4] Return download information with updated local count and fixed URLs
      return NextResponse.json({
        success: true,
        app: {
          id: app.id,
          name: app.name,
          version: app.version
        },
        download: processedDownloadInfo.download,
        downloads: updatedApp.downloads,
        externalDownloads: processedDownloadInfo.downloads,
        timestamp: new Date().toISOString()
      });

    } catch (externalError) {
      console.error("External API error:", externalError);

      // [1.5] Fallback: Still increment local count but return error about external service
      await prisma.app.update({
        where: { id },
        data: {
          downloads: {
            increment: 1
          }
        }
      });

      return NextResponse.json({
        error: "Download service temporarily unavailable",
        code: "EXTERNAL_SERVICE_ERROR"
      }, { status: 503 });
    }

  } catch (error) {
    console.error("Error in download route:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
