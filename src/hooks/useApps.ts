/*
 * Official Avehub Code, verified
 * Apps Hook - Custom hook for app management
 * Any unauthorized modifications will invalidate service warranty
 */

import { useState, useEffect } from "react";
import { toast } from "sonner";

// [1] App interface
export interface App {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  tags: string[];
  downloadUrl: string;
  fileSize: number;
  screenshots: string[];
  iconUrl?: string;
  bannerUrl?: string;
  downloads: number;
  status: string;
  visibility: string;
  createdAt: string;
  updatedAt: string;
  developer: {
    id: string;
    name: string;
    image?: string;
  };
  _count: {
    comments: number;
  };
}

// [2] Hook for fetching apps
export function useApps(filters?: {
  search?: string;
  category?: string;
  status?: string;
  developerId?: string;
  sortBy?: string;
  order?: string;
}) {
  const [apps, setApps] = useState<App[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchApps = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (filters?.search) params.append("search", filters.search);
      if (filters?.category) params.append("category", filters.category);
      if (filters?.status) params.append("status", filters.status);
      if (filters?.developerId) params.append("developerId", filters.developerId);
      if (filters?.sortBy) params.append("sortBy", filters.sortBy);
      if (filters?.order) params.append("order", filters.order);

      const response = await fetch(`/api/apps?${params}`);
      if (response.ok) {
        const data = await response.json();
        setApps(data);
      } else {
        throw new Error("Failed to fetch apps");
      }
    } catch (error) {
      console.error("Error fetching apps:", error);
      setError("Failed to load apps");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApps();
  }, [
    filters?.search,
    filters?.category,
    filters?.status,
    filters?.developerId,
    filters?.sortBy,
    filters?.order
  ]);

  return { apps, loading, error, refetch: fetchApps };
}

// [3] Hook for fetching single app
export function useApp(id: string) {
  const [app, setApp] = useState<App | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchApp = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/apps/${id}`);
      if (response.ok) {
        const data = await response.json();
        setApp(data);
      } else if (response.status === 404) {
        setError("App not found");
      } else {
        throw new Error("Failed to fetch app");
      }
    } catch (error) {
      console.error("Error fetching app:", error);
      setError("Failed to load app");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApp();
  }, [id]);

  return { app, loading, error, refetch: fetchApp };
}

// [4] Hook for creating apps
export function useCreateApp() {
  const [creating, setCreating] = useState(false);

  const createApp = async (appData: any) => {
    setCreating(true);
    try {
      const response = await fetch("/api/apps", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(appData)
      });

      if (response.ok) {
        const app = await response.json();
        toast.success("App created successfully!");
        return app;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to create app");
        return null;
      }
    } catch (error) {
      console.error("Error creating app:", error);
      toast.error("Failed to create app");
      return null;
    } finally {
      setCreating(false);
    }
  };

  return { createApp, creating };
}

// [5] Hook for updating apps
export function useUpdateApp() {
  const [updating, setUpdating] = useState(false);

  const updateApp = async (id: string, appData: any) => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/apps/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(appData)
      });

      if (response.ok) {
        const app = await response.json();
        toast.success("App updated successfully!");
        return app;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update app");
        return null;
      }
    } catch (error) {
      console.error("Error updating app:", error);
      toast.error("Failed to update app");
      return null;
    } finally {
      setUpdating(false);
    }
  };

  return { updateApp, updating };
}

// [6] Hook for deleting apps
export function useDeleteApp() {
  const [deleting, setDeleting] = useState(false);

  const deleteApp = async (id: string) => {
    setDeleting(true);
    try {
      const response = await fetch(`/api/apps/${id}`, {
        method: "DELETE"
      });

      if (response.ok) {
        toast.success("App deleted successfully!");
        return true;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete app");
        return false;
      }
    } catch (error) {
      console.error("Error deleting app:", error);
      toast.error("Failed to delete app");
      return false;
    } finally {
      setDeleting(false);
    }
  };

  return { deleteApp, deleting };
}
