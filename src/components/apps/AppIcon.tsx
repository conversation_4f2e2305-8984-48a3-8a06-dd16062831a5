/*
 * Official Avehub Code, verified
 * App Icon Component - Reusable app icon with fallback handling
 * Any unauthorized modifications will invalidate service warranty
 */

import { Package } from "lucide-react";
import Image from "next/image";

interface AppIconProps {
  iconUrl?: string;
  appName: string;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
}

const sizeClasses = {
  sm: "w-8 h-8",
  md: "w-16 h-16",
  lg: "w-24 h-24",
  xl: "w-32 h-32"
};

const iconSizes = {
  sm: { width: 32, height: 32 },
  md: { width: 64, height: 64 },
  lg: { width: 96, height: 96 },
  xl: { width: 128, height: 128 }
};

const fallbackIconSizes = {
  sm: "w-4 h-4",
  md: "w-8 h-8",
  lg: "w-12 h-12",
  xl: "w-16 h-16"
};

export function AppIcon({ iconUrl, appName, size = "md", className = "" }: AppIconProps) {
  // Get icon URL with proper handling
  const getIconUrl = (iconUrl?: string) => {
    if (!iconUrl) return null;
    
    // If it's already a full URL, use it
    if (iconUrl.startsWith('http')) {
      return iconUrl;
    }
    
    // If it's a CDN path, construct the full URL
    if (iconUrl.includes('/')) {
      const fileName = iconUrl.split('/').pop();
      return `https://cdn.avehubs.com/f/${fileName}`;
    }
    
    // If it's just a filename, construct the CDN URL
    return `https://cdn.avehubs.com/f/${iconUrl}`;
  };

  const iconSrc = getIconUrl(iconUrl);
  const sizeClass = sizeClasses[size];
  const { width, height } = iconSizes[size];
  const fallbackIconSize = fallbackIconSizes[size];

  return (
    <div className={`relative ${sizeClass} ${className}`}>
      {iconSrc ? (
        <Image
          src={iconSrc}
          alt={appName}
          width={width}
          height={height}
          className={`${sizeClass} rounded-lg object-cover`}
          onError={(e) => {
            // Fallback to placeholder on error
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            target.nextElementSibling?.classList.remove('hidden');
          }}
        />
      ) : null}
      <div className={`${sizeClass} bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center ${iconSrc ? 'hidden' : ''}`}>
        <Package className={`${fallbackIconSize} text-gray-500`} />
      </div>
    </div>
  );
}
