"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Star, Package, Download, MessageSquare } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

// App interface
interface App {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  downloads: number;
  createdAt: string;
  iconUrl?: string;
  _count: {
    comments: number;
  };
}

// Static fallback apps data (in case API fails)
const fallbackApps = [
  {
    name: "Ave Productivity Suite",
    description: "Boost your productivity with our all-in-one workspace solution",
    icon: "⚡",
    downloads: "50K+",
    rating: 4.9,
    category: "Productivity"
  },
  {
    name: "Ave Social Connect",
    description: "Connect with friends and communities in a whole new way",
    icon: "👥",
    downloads: "100K+",
    rating: 4.8,
    category: "Social"
  },
  {
    name: "Ave Creative Studio",
    description: "Unleash your creativity with powerful design tools",
    icon: "🎨",
    downloads: "75K+",
    rating: 4.9,
    category: "Creative"
  },
  {
    name: "Ave Security Shield",
    description: "Keep your digital life secure with advanced protection",
    icon: "🛡️",
    downloads: "200K+",
    rating: 5.0,
    category: "Security"
  },
  {
    name: "Ave Music Player",
    description: "Experience music like never before with our premium player",
    icon: "🎵",
    downloads: "150K+",
    rating: 4.7,
    category: "Entertainment"
  },
  {
    name: "Ave Fitness Tracker",
    description: "Track your fitness journey with advanced analytics",
    icon: "💪",
    downloads: "80K+",
    rating: 4.8,
    category: "Health"
  },
  {
    name: "Ave Photo Editor",
    description: "Professional photo editing tools at your fingertips",
    icon: "📸",
    downloads: "120K+",
    rating: 4.9,
    category: "Photography"
  },
  {
    name: "Ave Task Manager",
    description: "Organize your life with intelligent task management",
    icon: "📋",
    downloads: "90K+",
    rating: 4.6,
    category: "Productivity"
  }
];

export default function FeaturedApps() {
  const [featuredApps, setFeaturedApps] = useState<App[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch featured apps (6 latest approved apps)
  useEffect(() => {
    const fetchFeaturedApps = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/apps?status=APPROVED&sortBy=createdAt&order=desc&limit=6');
        if (response.ok) {
          const apps = await response.json();
          setFeaturedApps(apps);
        } else {
          // Use fallback data if API fails
          console.warn('Failed to fetch featured apps, using fallback data');
          setFeaturedApps([]);
        }
      } catch (error) {
        console.error('Error fetching featured apps:', error);
        setFeaturedApps([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedApps();
  }, []);

  // Format downloads
  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M+`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K+`;
    }
    return `${downloads}+`;
  };

  return (
    <section className="py-20 px-8">
      <div className="max-w-7xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Featured Applications
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Discover the latest and most innovative apps from our community
          </p>
        </motion.div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-3xl p-6 border border-purple-200/30 dark:border-purple-700/30 animate-pulse">
                <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-2xl mb-4"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="flex justify-between items-center">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
                </div>
              </div>
            ))}
          </div>
        ) : featuredApps.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No featured apps available
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Check back later for the latest applications
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredApps.map((app, index) => (
              <motion.div
                key={app.id}
                className="group relative bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-3xl p-6 border border-purple-200/30 dark:border-purple-700/30 hover:border-purple-400/50 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02, y: -5 }}
              >
                {/* App Icon */}
                <div className="mb-4">
                  {app.iconUrl ? (
                    <Image
                      src={app.iconUrl.startsWith('http') ? app.iconUrl : `https://cdn.avehubs.com/f/${app.iconUrl.split('/').pop()}`}
                      alt={app.name}
                      width={64}
                      height={64}
                      className="rounded-2xl"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center">
                      <Package className="w-8 h-8 text-white" />
                    </div>
                  )}
                </div>

                <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">{app.name}</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm line-clamp-2">
                  {app.shortDescription || app.description}
                </p>

                <div className="flex items-center justify-between mb-4">
                  <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full">
                    {app.category}
                  </span>
                  <div className="flex items-center gap-1">
                    <MessageSquare className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">{app._count.comments}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    <Download className="w-4 h-4 inline mr-1" />
                    {formatDownloads(app.downloads)} downloads
                  </span>
                  <Link href={`/apps/${app.id}`}>
                    <motion.button
                      className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-xl text-sm font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      View App
                    </motion.button>
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}
