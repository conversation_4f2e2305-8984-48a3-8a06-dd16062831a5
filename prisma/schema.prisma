datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// prisma/schema.prisma
model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  admin         <PERSON>an   @default(false)
  isDeveloper   Boolean   @default(false)
  accounts      Account[]
  apps          App[]
  appComments   AppComment[]
}

model Account {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?  @db.String
  access_token      String?  @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?  @db.String
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Announcement {
  id          String   @id @default(uuid()) @map("_id")
  title       String
  description String
  footer      String?
  imageUrl    String?
  createdAt   DateTime @default(now())
}

model PartnershipApplication {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @unique @db.ObjectId
  username    String
  companyName String
  websiteUrl  String
  reason      String
  experience  String
  additional  String?
  status      String   @default("pending") // 'pending', 'accepted', 'rejected'
  appliedAt   DateTime @default(now())
  statusUpdatedAt DateTime?
}

model StaffApplication {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  userId          String   @unique @db.ObjectId
  fullName        String
  age             Int
  location        String
  availability    String // 'full-time', 'part-time', 'weekends'
  position        String // 'moderator', 'developer', 'designer', 'support', 'other'
  experience      String
  skills          String
  motivation      String
  portfolioUrl    String?
  additionalInfo  String?
  status          String   @default("pending") // 'pending', 'accepted', 'rejected'
  appliedAt       DateTime @default(now())
  statusUpdatedAt DateTime?
}

model Plugin {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  publisherId     String    @db.ObjectId
  publisherName   String
  name            String
  description     String
  version         String
  releaseType     String    @default("release") // "release" or "snapshot"
  releaseDate     DateTime  @default(now())
  updateDate      DateTime  @updatedAt
  fileLocation    String
  downloads       Int       @default(0)
  comments        PluginComment[]
}

model PluginComment {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  pluginId        String    @db.ObjectId
  userId          String    @db.ObjectId
  userName        String
  content         String
  createdAt       DateTime  @default(now())
  plugin          Plugin    @relation(fields: [pluginId], references: [id], onDelete: Cascade)
}

model App {
  id              String      @id @default(auto()) @map("_id") @db.ObjectId
  developerId     String      @db.ObjectId
  name            String
  description     String
  shortDescription String?
  version         String
  category        String
  tags            String[]    @default([])
  downloadUrl     String
  fileSize        Int         // in bytes
  screenshots     String[]    @default([])
  iconUrl         String?
  bannerUrl       String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  downloads       Int         @default(0)
  status          AppStatus   @default(PENDING)
  visibility      AppVisibility @default(PUBLIC)
  featured        Boolean     @default(false)
  minVersion      String?     // minimum system version required
  maxVersion      String?     // maximum system version supported
  website         String?
  supportEmail    String?
  changelog       String?
  developer       User        @relation(fields: [developerId], references: [id], onDelete: Cascade)
  comments        AppComment[]
  versions        AppVersion[]

  @@index([status])
  @@index([category])
  @@index([featured])
  @@index([developerId])
}

model AppVersion {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  appId           String    @db.ObjectId
  version         String
  downloadUrl     String
  fileSize        Int
  changelog       String?
  createdAt       DateTime  @default(now())
  app             App       @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@unique([appId, version])
}

model AppComment {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  appId           String    @db.ObjectId
  userId          String    @db.ObjectId
  content         String
  rating          Int?      // 1-5 star rating
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  app             App       @relation(fields: [appId], references: [id], onDelete: Cascade)
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([appId])
  @@index([userId])
}

enum AppStatus {
  PENDING
  APPROVED
  REJECTED
  SUSPENDED
}

enum AppVisibility {
  PUBLIC
  PRIVATE
  UNLISTED
}

